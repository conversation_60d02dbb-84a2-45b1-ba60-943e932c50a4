# 机器人批量授权功能实现

## 概述

在 `src/app/courseware/robot/list/page.tsx` 页面中实现了"一键授权"和"一键关闭"功能，参考了 `src/app/courseware/agent/detail/page.tsx` 页面中 `RobotCoursesCard` 组件的实现，并增加了 `robotIds` 参数支持。

## 主要修改

### 1. 页面功能增强 (`src/app/courseware/robot/list/page.tsx`)

#### 新增导入
```typescript
import { courseService } from "@/services/course";
```

#### 新增状态管理
```typescript
// 批量授权状态管理
const [batchAuthorizing, setBatchAuthorizing] = useState(false);
```

#### 新增核心功能函数

##### 获取选中机器人ID列表
```typescript
const getSelectedRobotIds = (): string[] => {
  return Object.entries(selectedRobots)
    .filter(([_, selected]) => selected)
    .map(([robotId, _]) => robotId);
};
```

##### 批量授权/关闭处理函数
```typescript
const handleBatchAuthorization = async (enable: boolean) => {
  const selectedRobotIds = getSelectedRobotIds();
  
  if (selectedRobotIds.length === 0) {
    toastService.error('请先选择要操作的机器人');
    return;
  }

  // 从URL获取必要参数
  const agentProvince = searchParams.get('agentProvince') || '';
  const agentCity = searchParams.get('agentCity') || '';
  const agentArea = searchParams.get('agentArea') || '';
  const agentId = searchParams.get('agentId') || '';
  const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';

  if (!agentProvince || !agentCity || !agentId) {
    toastService.error('缺少必要的授权参数');
    return;
  }

  try {
    setBatchAuthorizing(true);

    const params = {
      province: agentProvince,
      city: agentCity,
      area: agentArea,
      agenId: agentId ? parseInt(agentId) : undefined,
      stageType,
      robotIds: selectedRobotIds // 关键新增参数
    };

    if (enable) {
      await courseService.enableAgentCourse(params);
      toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键授权`);
    } else {
      await courseService.disableAgentCourse(params);
      toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键关闭`);
    }

  } catch (error) {
    const errorMessage = error instanceof ApiClientError
      ? error.message
      : `一键${enable ? '授权' : '关闭'}失败`;
    toastService.error(errorMessage);
  } finally {
    setBatchAuthorizing(false);
  }
};
```

#### 按钮UI更新
将原来的静态按钮替换为功能性按钮：

```typescript
<button
  onClick={() => handleBatchAuthorization(true)}
  disabled={batchAuthorizing}
  className={cn(
    "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
    batchAuthorizing
      ? "bg-gray-500 cursor-not-allowed"
      : "bg-green-600 hover:bg-green-700"
  )}
>
  {batchAuthorizing ? "授权中..." : "一键授权"}
</button>

<button
  onClick={() => handleBatchAuthorization(false)}
  disabled={batchAuthorizing}
  className={cn(
    "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
    batchAuthorizing
      ? "bg-gray-500 cursor-not-allowed"
      : "bg-red-600 hover:bg-red-700"
  )}
>
  {batchAuthorizing ? "关闭中..." : "一键关闭"}
</button>
```

### 2. 表格组件优化 (`src/components/robots-table.tsx`)

#### 接口更新
```typescript
interface RobotsTableProps {
  robots: Robot[];
  totalRobots: number;
  selectedRobots?: Record<string, boolean>; // 新增：外部选择状态
  onSelectChange?: (robotId: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  pageSize?: number;
}
```

#### 状态管理优化
- 移除内部选择状态管理
- 使用父组件传递的 `selectedRobots` 状态
- 优化全选逻辑，基于当前页面的机器人计算全选状态

#### 传递选择状态
在页面中传递选择状态给表格组件：
```typescript
<RobotsTable
  robots={robots}
  totalRobots={totalRobots}
  selectedRobots={selectedRobots} // 新增
  onSelectChange={handleSelectChange}
  onSelectAll={handleSelectAll}
/>
```

## API 参数说明

### 新增参数
- `robotIds: string[]` - 选中的机器人ID列表

### 完整API参数结构
```typescript
{
  province: string,        // 省份
  city: string,           // 城市
  area?: string,          // 区域
  agenId?: number,        // 代理商ID
  stageType: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY', // 阶段类型
  robotIds: string[]      // 机器人ID列表（新增）
}
```

## 功能特点

1. **选择验证**: 操作前检查是否选中机器人
2. **参数验证**: 确保必要的授权参数完整
3. **状态管理**: 防止重复操作，显示操作状态
4. **错误处理**: 完善的错误提示和日志记录
5. **用户反馈**: 成功/失败消息提示，显示操作的机器人数量
6. **UI一致性**: 与原有设计风格保持一致

## 测试

创建了测试页面 `src/app/test-robot-batch-auth/page.tsx` 用于验证功能实现。

## 使用方式

1. 在机器人列表页面选择要操作的机器人（通过复选框）
2. 点击"一键授权"按钮对选中的机器人执行课程授权
3. 点击"一键关闭"按钮对选中的机器人执行课程关闭
4. 系统会显示操作进度和结果反馈
