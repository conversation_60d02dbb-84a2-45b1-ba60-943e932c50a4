"use client";

import Link from "next/link";
import { AppLayout } from "@/components/app-layout";

export default function TestRobotFlowPage() {
  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-white mb-8">机器人台数跳转功能测试</h1>
        
        <div className="space-y-8">
          {/* 测试说明 */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">测试流程说明</h2>
            <div className="text-gray-300 space-y-2">
              <p>1. 点击下面的链接进入代理商详情页面</p>
              <p>2. 在代理商详情页面点击机器人台数（如"25台"）</p>
              <p>3. 应该跳转到机器人列表页面，并显示该代理商的机器人详细信息</p>
              <p>4. 机器人列表页面会调用 /agent/detail 接口获取真实的SN信息</p>
            </div>
          </div>
          
          {/* 测试链接 */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">测试链接</h2>
            <div className="space-y-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">测试案例 1: 成都科技公司</h3>
                <Link
                  href="/courseware/agent/detail?agentId=123&agentProvince=四川省&agentCity=成都市&agentArea=金牛区&agentName=成都科技公司&agentBots=25&stageType=PRIMARY&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  进入代理商详情页面 (成都科技公司, 25台机器人)
                </Link>
                <p className="text-gray-400 text-sm mt-1">
                  参数: 四川省成都市金牛区, 25台机器人, PRIMARY阶段
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">测试案例 2: 北京教育公司</h3>
                <Link
                  href="/courseware/agent/detail?agentId=456&agentProvince=北京市&agentCity=北京市&agentArea=朝阳区&agentName=北京教育公司&agentBots=15&stageType=PRESCHOOL&action=detail"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  进入代理商详情页面 (北京教育公司, 15台机器人)
                </Link>
                <p className="text-gray-400 text-sm mt-1">
                  参数: 北京市朝阳区, 15台机器人, PRESCHOOL阶段
                </p>
              </div>
            </div>
          </div>
          
          {/* 直接测试机器人列表页面 */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">直接测试机器人列表页面</h2>
            <div className="space-y-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">直接访问机器人列表</h3>
                <Link
                  href="/courseware/robot/list?agentProvince=四川省&agentCity=成都市&agentArea=金牛区&agentName=成都科技公司&agentBots=25&agentId=123&stageType=PRIMARY"
                  className="text-blue-400 hover:text-blue-300 underline"
                >
                  直接访问机器人列表页面
                </Link>
                <p className="text-gray-400 text-sm mt-1">
                  这个链接会直接调用 /agent/detail 接口获取机器人信息
                </p>
              </div>
            </div>
          </div>
          
          {/* 功能说明 */}
          <div>
            <h2 className="text-lg font-semibold text-white mb-4">实现的功能</h2>
            <div className="text-gray-300 space-y-2">
              <p>✅ 在代理商详情页面的机器人台数区域添加了点击事件</p>
              <p>✅ 点击后跳转到机器人列表页面，传递所有必要的代理商信息</p>
              <p>✅ 机器人列表页面从URL参数获取代理商信息</p>
              <p>✅ 调用 /agent/detail 接口获取详细的SN信息</p>
              <p>✅ 将省市区信息拼接显示在"机器人最后获取位置"字段</p>
              <p>✅ 机器人台数从URL参数获取，不使用固定值</p>
              <p>✅ 保持现有页面布局和样式不变</p>
              <p>✅ 保留所有现有按钮和功能</p>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
