"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toastService } from "@/services/toast";

export default function TestBatchControlFlowPage() {
  const router = useRouter();
  const [selectedRobotIds, setSelectedRobotIds] = useState<string[]>([
    "robot001", "robot002", "robot003"
  ]);

  // 模拟代理商信息
  const agentInfo = {
    agentProvince: "四川省",
    agentCity: "徐州市",
    agentArea: "云龙区",
    agentName: "元传研和计及",
    agentId: "123",
    stageType: "PRIMARY"
  };

  const handleBatchControl = () => {
    if (selectedRobotIds.length === 0) {
      toastService.error('请先选择要控制的机器人');
      return;
    }

    // 构建跳转URL参数
    const params = new URLSearchParams({
      agentProvince: agentInfo.agentProvince,
      agentCity: agentInfo.agentCity,
      agentArea: agentInfo.agentArea,
      agentName: agentInfo.agentName,
      agentId: agentInfo.agentId,
      agentBots: selectedRobotIds.length.toString(), // 机器人台数为选中的数量
      stageType: agentInfo.stageType,
      robotIds: selectedRobotIds.join(','), // 将选中的robotIds作为逗号分隔的字符串传递
      action: 'batchControl'
    });

    // 跳转到代理商详情页
    router.push(`/courseware/agent/detail?${params.toString()}`);
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>批量控制流程测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 代理商信息显示 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">代理商信息</h3>
              <div className="bg-gray-100 p-4 rounded space-y-2">
                <p><strong>代理商名称:</strong> {agentInfo.agentName}</p>
                <p><strong>省份:</strong> {agentInfo.agentProvince}</p>
                <p><strong>城市:</strong> {agentInfo.agentCity}</p>
                <p><strong>区域:</strong> {agentInfo.agentArea}</p>
                <p><strong>代理商ID:</strong> {agentInfo.agentId}</p>
                <p><strong>阶段类型:</strong> {agentInfo.stageType}</p>
              </div>
            </div>

            {/* 机器人选择 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">选择机器人</h3>
              <div className="space-y-2">
                {["robot001", "robot002", "robot003", "robot004", "robot005"].map(robotId => (
                  <label key={robotId} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedRobotIds.includes(robotId)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedRobotIds(prev => [...prev, robotId]);
                        } else {
                          setSelectedRobotIds(prev => prev.filter(id => id !== robotId));
                        }
                      }}
                    />
                    <span>{robotId}</span>
                  </label>
                ))}
              </div>
              <p className="text-sm text-gray-600 mt-2">
                已选择 {selectedRobotIds.length} 台机器人: {selectedRobotIds.join(', ')}
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-4">
              <Button
                onClick={handleBatchControl}
                disabled={selectedRobotIds.length === 0}
                className="bg-amber-500 hover:bg-amber-600"
              >
                批量控制
              </Button>
            </div>

            {/* 流程说明 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">流程说明</h3>
              <div className="bg-blue-50 p-4 rounded space-y-2">
                <p><strong>1.</strong> 选择要控制的机器人</p>
                <p><strong>2.</strong> 点击"批量控制"按钮</p>
                <p><strong>3.</strong> 跳转到代理商详情页面，携带以下参数：</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>代理商信息（省份、城市、区域、名称、ID）</li>
                  <li>机器人台数（选中的机器人数量）</li>
                  <li>robotIds（逗号分隔的机器人ID列表）</li>
                  <li>阶段类型</li>
                </ul>
                <p><strong>4.</strong> 在代理商详情页面中，所有授权按钮都会携带 robotIds 参数</p>
                <p><strong>5.</strong> 机器人台数显示为选中的机器人数量</p>
              </div>
            </div>

            {/* 预期URL */}
            <div>
              <h3 className="text-lg font-semibold mb-2">预期跳转URL</h3>
              <div className="bg-gray-100 p-4 rounded">
                <code className="text-sm break-all">
                  /courseware/agent/detail?agentProvince={agentInfo.agentProvince}&agentCity={agentInfo.agentCity}&agentArea={agentInfo.agentArea}&agentName={agentInfo.agentName}&agentId={agentInfo.agentId}&agentBots={selectedRobotIds.length}&stageType={agentInfo.stageType}&robotIds={selectedRobotIds.join(',')}&action=batchControl
                </code>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
