"use client";

import { useState } from "react";
import { AppLayout } from "@/components/app-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { courseService } from "@/services/course";
import { toastService } from "@/services/toast";
import { ApiClientError } from "@/services/api";

export default function TestRobotBatchAuthPage() {
  const [batchAuthorizing, setBatchAuthorizing] = useState(false);
  const [selectedRobotIds, setSelectedRobotIds] = useState<string[]>([
    "robot001", "robot002", "robot003"
  ]);

  // 测试参数
  const testParams = {
    province: "广东省",
    city: "深圳市", 
    area: "南山区",
    agenId: 123,
    stageType: "PRIMARY" as const
  };

  const handleBatchAuthorization = async (enable: boolean) => {
    if (selectedRobotIds.length === 0) {
      toastService.error('请先选择要操作的机器人');
      return;
    }

    try {
      setBatchAuthorizing(true);

      const params = {
        ...testParams,
        robotIds: selectedRobotIds
      };

      console.log('Batch authorization params:', params);

      if (enable) {
        await courseService.enableAgentCourse(params);
        toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键授权`);
      } else {
        await courseService.disableAgentCourse(params);
        toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键关闭`);
      }

    } catch (error) {
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : `一键${enable ? '授权' : '关闭'}失败`;
      toastService.error(errorMessage);
      console.error(`Failed to batch ${enable ? 'enable' : 'disable'} courses:`, error);
    } finally {
      setBatchAuthorizing(false);
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>机器人批量授权测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 测试参数显示 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">测试参数</h3>
              <div className="bg-gray-100 p-4 rounded space-y-2">
                <p><strong>省份:</strong> {testParams.province}</p>
                <p><strong>城市:</strong> {testParams.city}</p>
                <p><strong>区域:</strong> {testParams.area}</p>
                <p><strong>代理商ID:</strong> {testParams.agenId}</p>
                <p><strong>阶段类型:</strong> {testParams.stageType}</p>
                <p><strong>选中的机器人ID:</strong> {selectedRobotIds.join(', ')}</p>
              </div>
            </div>

            {/* 机器人选择 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">选择机器人</h3>
              <div className="space-y-2">
                {["robot001", "robot002", "robot003", "robot004", "robot005"].map(robotId => (
                  <label key={robotId} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedRobotIds.includes(robotId)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedRobotIds(prev => [...prev, robotId]);
                        } else {
                          setSelectedRobotIds(prev => prev.filter(id => id !== robotId));
                        }
                      }}
                    />
                    <span>{robotId}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-4">
              <Button
                onClick={() => handleBatchAuthorization(true)}
                disabled={batchAuthorizing || selectedRobotIds.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                {batchAuthorizing ? "授权中..." : "一键授权"}
              </Button>
              <Button
                onClick={() => handleBatchAuthorization(false)}
                disabled={batchAuthorizing || selectedRobotIds.length === 0}
                className="bg-red-600 hover:bg-red-700"
              >
                {batchAuthorizing ? "关闭中..." : "一键关闭"}
              </Button>
            </div>

            {/* API信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">API信息</h3>
              <div className="bg-gray-100 p-4 rounded space-y-2">
                <p><strong>启用课程端点:</strong> POST /course-grade/agent/enabled</p>
                <p><strong>禁用课程端点:</strong> POST /course-grade/agent/disabled</p>
                <p><strong>新增参数:</strong> robotIds (string[]) - 选中的机器人ID列表</p>
                <p><strong>功能:</strong> 对选中的机器人执行批量课程授权/关闭操作</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
