# 批量控制功能实现

## 概述

实现了从机器人列表页面到代理商详情页面的批量控制功能，包括参数传递、状态管理和授权操作的完整流程。

## 功能流程

### 1. 机器人列表页面 (`src/app/courseware/robot/list/page.tsx`)

#### 新增功能
- **批量控制按钮**: 点击后跳转到代理商详情页面
- **参数传递**: 携带选中的机器人ID和代理商信息
- **选择验证**: 确保选中机器人后才能操作

#### 关键实现

##### 导入路由功能
```typescript
import { useSearchParams, useRouter } from "next/navigation";
```

##### 批量控制处理函数
```typescript
const handleBatchControl = () => {
  const selectedRobotIds = getSelectedRobotIds();
  
  if (selectedRobotIds.length === 0) {
    toastService.error('请先选择要控制的机器人');
    return;
  }

  // 从URL获取代理商信息
  const agentProvince = searchParams.get('agentProvince') || '';
  const agentCity = searchParams.get('agentCity') || '';
  const agentArea = searchParams.get('agentArea') || '';
  const agentName = searchParams.get('agentName') || '';
  const agentId = searchParams.get('agentId') || '';
  const stageType = searchParams.get('stageType') || 'UNKNOWN';

  // 构建跳转URL参数
  const params = new URLSearchParams({
    agentProvince,
    agentCity,
    agentArea,
    agentName,
    agentId,
    agentBots: selectedRobotIds.length.toString(), // 机器人台数为选中的数量
    stageType,
    robotIds: selectedRobotIds.join(','), // 将选中的robotIds作为逗号分隔的字符串传递
    action: 'batchControl'
  });

  // 跳转到代理商详情页
  router.push(`/courseware/agent/detail?${params.toString()}`);
};
```

##### 按钮实现
```typescript
<button 
  onClick={handleBatchControl}
  className="inline-flex items-center justify-center rounded-full bg-amber-500 hover:bg-amber-600 text-white text-xs px-3 py-0.5 h-6 font-medium"
>
  批量控制
</button>
```

### 2. 代理商详情页面 (`src/app/courseware/agent/detail/page.tsx`)

#### 新增功能
- **robotIds 参数接收**: 从URL获取机器人ID列表
- **参数传递**: 将 robotIds 传递给 RobotCoursesCard 组件

#### 关键实现

##### 参数传递
```typescript
const componentRobotIds = searchParams.get('robotIds') || '';

<RobotCoursesCard
  agentData={agentData}
  province={componentProvince}
  city={componentCity}
  area={componentArea}
  agentId={componentAgentId}
  stageType={componentStageType}
  robotIds={componentRobotIds}
/>
```

### 3. 机器人课程卡片组件 (`src/components/robot-courses-card.tsx`)

#### 新增功能
- **robotIds 参数支持**: 接收和处理机器人ID列表
- **授权函数增强**: 所有授权操作都携带 robotIds 参数
- **智能消息提示**: 显示操作的机器人数量

#### 关键实现

##### 接口更新
```typescript
interface RobotCoursesCardProps {
  agentData: RobotCourses;
  province?: string;
  city?: string;
  area?: string;
  agentId?: string;
  stageType?: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY';
  robotIds?: string; // 新增：机器人ID列表（逗号分隔的字符串）
}
```

##### 单个课程授权增强
```typescript
const params = {
  province,
  city,
  area,
  agenId: agentId ? parseInt(agentId) : undefined,
  stageType,
  courseId: parseInt(courseId),
  robotIds: robotIds ? robotIds.split(',').filter(id => id.trim()) : undefined
};
```

##### 批量授权增强
```typescript
const params = {
  province,
  city,
  area,
  agenId: agentId ? parseInt(agentId) : undefined,
  stageType,
  robotIds: robotIds ? robotIds.split(',').filter(id => id.trim()) : undefined
};

const robotCount = robotIds ? robotIds.split(',').filter(id => id.trim()).length : 0;
const successMessage = robotCount > 0 
  ? `已对 ${robotCount} 台机器人执行一键${enable ? '授权' : '关闭'}`
  : `一键${enable ? '授权' : '关闭'}成功`;
```

## 参数传递流程

### URL 参数结构
```
/courseware/agent/detail?
  agentProvince=四川省&
  agentCity=徐州市&
  agentArea=云龙区&
  agentName=元传研和计及&
  agentId=123&
  agentBots=3&              // 选中的机器人数量
  stageType=PRIMARY&
  robotIds=robot001,robot002,robot003&  // 逗号分隔的机器人ID
  action=batchControl
```

### 数据转换
1. **列表页面**: `string[]` → `string` (逗号分隔)
2. **详情页面**: `string` → 传递给组件
3. **组件内部**: `string` → `string[]` (分割处理)

## API 参数增强

### 所有授权相关API现在都支持 robotIds 参数

#### 单个课程授权
```typescript
{
  province: string,
  city: string,
  area?: string,
  agenId?: number,
  stageType: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY',
  courseId: number,
  robotIds?: string[]  // 新增
}
```

#### 批量授权
```typescript
{
  province: string,
  city: string,
  area?: string,
  agenId?: number,
  stageType: 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY',
  robotIds?: string[]  // 新增
}
```

## 功能特点

1. **完整的参数传递链**: 从选择到授权的完整数据流
2. **智能台数显示**: 机器人台数显示为选中的数量
3. **统一的授权接口**: 所有授权按钮都支持 robotIds 参数
4. **用户友好的反馈**: 显示具体操作的机器人数量
5. **数据验证**: 确保选中机器人后才能操作
6. **向后兼容**: 不影响原有功能的正常使用

## 测试

创建了测试页面 `src/app/test-batch-control-flow/page.tsx` 用于验证完整的批量控制流程。

## 使用方式

1. 在机器人列表页面选择要控制的机器人
2. 点击"批量控制"按钮
3. 自动跳转到代理商详情页面，显示选中的机器人数量
4. 在详情页面中，所有授权操作都会针对选中的机器人执行
5. 获得包含机器人数量的操作反馈
